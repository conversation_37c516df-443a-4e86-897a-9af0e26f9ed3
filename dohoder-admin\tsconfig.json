{
  "extends": "../tsconfig.base.json",
  "compilerOptions": {
    "jsx": "preserve",
    "noEmit": true,
    "emitDeclarationOnly": false,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      // Local app paths
      "@/*": ["./*"],

      // Shared library paths (inherit from base + override for this app)
      "@/domain/*": ["../libs/domain/src/*"],
      "@/data-access/*": ["../libs/data-access/src/*"],
      "@/ui/*": ["../libs/ui/src/*"],
      "@/utils/*": ["../libs/util/src/*"],

      // Barrel exports
      "@/domain": ["../libs/domain/src/index.ts"],
      "@/data-access": ["../libs/data-access/src/index.ts"],
      "@/ui": ["../libs/ui/src/index.ts"],
      "@/utils": ["../libs/util/src/index.ts"]
    },
    "types": ["jest", "node"],
    "outDir": "dist",
    "baseUrl": "."
  },
  "include": [
    "../dist/dohoder-admin/.next/types/**/*.ts",
    "../dohoder-admin/.next/types/**/*.ts",
    "app/**/*.js",
    "app/**/*.jsx",
    "app/**/*.ts",
    "app/**/*.tsx",
    "next-env.d.ts",
    ".next/types/**/*.ts",
    "../libs/domain/src/**/*.ts",
    "../libs/domain/src/**/*.tsx",
    "../libs/data-access/src/**/*.ts",
    "../libs/data-access/src/**/*.tsx",
    "../libs/ui/src/**/*.ts",
    "../libs/ui/src/**/*.tsx",
    "../libs/util/src/**/*.ts",
    "../libs/util/src/**/*.tsx"
  ],
  "exclude": [
    "out-tsc",
    "dist",
    "node_modules",
    "jest.config.ts",
    "app/**/*.spec.ts",
    "app/**/*.test.ts",
    "src/**/*.spec.ts",
    "src/**/*.test.ts",
    ".next",
    "eslint.config.js",
    "eslint.config.cjs",
    "eslint.config.mjs"
  ]
}
