{
  "compilerOptions": {
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "emitDeclarationOnly": false,
    "importHelpers": true,
    "isolatedModules": true,
    "lib": ["es2022", "dom", "dom.iterable"],
    "module": "esnext",
    "moduleResolution": "bundler",
    "noEmitOnError": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noImplicitReturns": true,
    "noUnusedLocals": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "es2022",
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "customConditions": ["@dohoder/source"],
    "paths": {
      // Modern clean imports with @ prefix
      "@/*": ["*"],
      "@/domain/*": ["libs/domain/src/*"],
      "@/data-access/*": ["libs/data-access/src/*"],
      "@/ui/*": ["libs/ui/src/*"],
      "@/utils/*": ["libs/util/src/*"],

      // Barrel exports for convenience
      "@/domain": ["libs/domain/src/index.ts"],
      "@/data-access": ["libs/data-access/src/index.ts"],
      "@/ui": ["libs/ui/src/index.ts"],
      "@/utils": ["libs/util/src/index.ts"],

      // Legacy support (keep for backward compatibility during migration)
      "@dohoder/domain/*": ["libs/domain/src/*.ts"],
      "@dohoder/data-access/*": ["libs/data-access/src/*.ts"],
      "@dohoder/ui/*": ["libs/ui/src/*.ts"],
      "@dohoder/util/*": ["libs/util/src/*.ts"]
    }
  }
}
